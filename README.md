# VBPJS RSKD - Sistem Verifikasi BPJS

## Deskripsi Proyek
VBPJS RSKD adalah sistem verifikasi BPJS yang dikembangkan untuk Rumah Sakit. Sistem ini membantu dalam proses verifikasi pasien BPJS, pengelolaan data pasien, dan integrasi dengan berbagai layanan rumah sakit seperti farmasi, radiologi, pat<PERSON><PERSON> anatomi (PA), dan rehabilitasi.

## Fitur Utama

### 1. Manajemen Pasien
- Pencarian data pasien berdasarkan nomor BPJS atau nomor RM
- Pengelolaan data pasien BPJS

### 2. Verifikasi BPJS
- Verifikasi awal pasien BPJS
- Pengelolaan layanan verifikasi
- Pengelolaan dokumen verifikasi

### 3. <PERSON><PERSON>l <PERSON>
- Pengelolaan resep obat
- Verifikasi resep
- Pencatatan hasil pemeriksaan laboratorium

### 4. Modul Radiologi
- Pengelolaan pemeriksaan radiologi
- Verifikasi hasil radiologi

### 5. <PERSON><PERSON><PERSON> (PA)
- <PERSON>gel<PERSON><PERSON> pemeriksaan sitologi, histo<PERSON>i, im<PERSON>histok<PERSON><PERSON>, dan molekuler
- Verifikasi hasil pemeriksaan

### 6. Modul Rehabilitasi
- Pengelolaan layanan rehabilitasi

## Teknologi yang Digunakan

### Backend
- PHP dengan framework CodeIgniter 3
- MySQL Database

### Frontend
- HTML, CSS, JavaScript
- jQuery
- Bootstrap
- AdminLTE template
- DataTables dengan server-side processing

## Solusi Teknis Implementasi

Sistem ini menerapkan beberapa solusi teknis untuk mengatasi masalah umum:

### 1. Sinkronisasi Checkbox dengan Database
Implementasi solusi untuk mempertahankan state checkbox saat pagination atau pencarian pada DataTables dengan server-side processing. Solusi ini memastikan data yang disubmit ke database sesuai dengan interaksi pengguna.

### 2. Preservasi State Checkbox
Penerapan dual-array tracking untuk mempertahankan state checkbox saat navigasi halaman atau pencarian:
- Array untuk item yang dipilih (selected)
- Array untuk item yang tidak dipilih (unselected)

## Struktur Proyek

```
├── application/           # Direktori utama aplikasi CodeIgniter
│   ├── config/           # File konfigurasi
│   ├── controllers/      # Controller aplikasi
│   ├── models/           # Model untuk akses database
│   └── views/            # File tampilan
├── assets/               # Aset statis (CSS, JS, gambar)
│   ├── css/
│   ├── dist/
│   ├── fonts/
│   ├── images/
│   ├── js/
│   └── plugins/
├── system/               # Core system CodeIgniter
└── user_guide/           # Dokumentasi CodeIgniter
```

## Instalasi dan Penggunaan

### Prasyarat
- PHP 7.2 atau lebih tinggi
- MySQL/MariaDB
- Web server (Apache/Nginx)

### Langkah Instalasi
1. Clone repositori ini ke direktori web server Anda
2. Konfigurasi database di `application/config/database.php`
3. Pastikan direktori `assets/images/captcha` memiliki izin tulis
4. Akses aplikasi melalui browser

### Login
Gunakan kredensial yang telah diberikan untuk masuk ke sistem. Sistem menggunakan CAPTCHA untuk keamanan tambahan.

## Pengembangan

Proyek ini menggunakan CodeIgniter 3 dengan struktur MVC standar. Untuk pengembangan lebih lanjut:

1. Tambahkan controller baru di `application/controllers/`
2. Buat model terkait di `application/models/`
3. Buat tampilan di `application/views/`

## Lisensi

Hak Cipta © RSKD - Sistem dikembangkan untuk penggunaan internal rumah sakit.